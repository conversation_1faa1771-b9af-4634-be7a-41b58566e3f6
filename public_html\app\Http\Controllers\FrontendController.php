<?php

namespace App\Http\Controllers;

use Share;
use Carbon\Carbon;
use Stripe\Product;
use App\Models\Size;
use App\Models\Color;
use App\Models\category;
use App\Models\Coupon;
use App\Models\FaqTab;
use App\Models\Inventory;
use App\Models\Thumbnail;
use App\Models\WishTable;
use App\Models\Subcategory;
use App\Models\Product_list;
use Illuminate\Http\Request;
use Jorenvh\Share\ShareFacade;
use App\Models\OrdereditemsTab;
use App\Models\SubsTab;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;
use App\Models\CustomizationRequest;
use App\Models\User;
use App\Notifications\CustomizationRequestSubmitted;

class FrontendController extends Controller
{
    // === Frontend Home ===
    function home_page(){
        $cata_all = category::take(7)->get();
        $cata_all_in = category::all();
        $product_all = Product_list::inRandomOrder()->take(8)->get();
        // $product_all = Product_list::all();
        $product_recent = Product_list::orderBy('updated_at','desc')->take(3)->get();

        $top_seller = OrdereditemsTab::groupBy('product_id')
        ->selectRaw('sum(quantity) as sum, product_id')
        ->orderBy('sum', 'DESC')
        ->take(3)
        ->get();

        $featured = OrdereditemsTab::whereNotNull('star')
        ->groupBy('product_id')
        ->selectRaw('sum(star) as sum, product_id')
        ->orderBy('sum', 'DESC')
        ->take(3)
        ->get();

        return view('frontend.index', [
            'cata_all' => $cata_all,
            'cata_all_in' => $cata_all_in,
            'product_all' => $product_all,
            'product_recent' => $product_recent,
            'top_seller' => $top_seller,
            'featured' => $featured,
        ]);
    }

    // === Frontend About ===
    function about_page(){
        return view('frontend.about');
    }





    // === Frontend Shop ===
    function shop_page(Request $request){
        $cate_all = category::orderBy('cata_name')->get();
        $subcate_all = Subcategory::orderBy('sub_cata_name')->get();
        $color_all = Color::all();
        // $color_inv = Inventory::select('color')->distinct()->get();
        $brand_all = Product_list::orderBy('brand')->whereNotNull('brand')->get()->unique('brand');
        $size_type = Size::where('size_type', '!=', 'N/A')->get()->unique('size_type');
        $size_all = Size::all();

        $data = $request->all();
        $sorting = 'created_at';
        $sort_type = 'DESC';
        $showing = '9';

        $searched_items = Product_list::where(function($q) use($data) {
            // Search in product name, description
            if (!empty($data['inp'])) {
                $searchTerm = '%' . trim($data['inp']) . '%';
                $q->where(function($query) use ($searchTerm) {
                    $query->where('product_name', 'like', $searchTerm)
                          ->orWhere('short_desc', 'like', $searchTerm)
                          ->orWhere('long_desc', 'like', $searchTerm);
                });
            }

            // Category filter
            if (!empty($data['cate'])) {
                $q->where('cata_id', $data['cate']);
            }

            // Subcategory filter - handle both single and multiple subcategories
            if (!empty($data['subcate'])) {
                if (strpos($data['subcate'], ',') !== false) {
                    // Multiple subcategories (comma-separated)
                    $subcateIds = array_map('trim', explode(',', $data['subcate']));
                    $subcateIds = array_filter($subcateIds, 'is_numeric'); // Only keep numeric values
                    if (!empty($subcateIds)) {
                        $q->whereIn('subcata_id', $subcateIds);
                    }
                } else {
                    // Single subcategory
                    $q->where('subcata_id', $data['subcate']);
                }
            }

            // Brand filter
            if (!empty($data['brand'])) {
                $q->where('brand', $data['brand']);
            }

            // Price range filter
            if (!empty($data['min']) || !empty($data['max'])) {
                $min = !empty($data['min']) ? $data['min'] : 0;
                $max = !empty($data['max']) ? $data['max'] : 999999;
                $q->whereBetween('after_disc', [$min, $max]);
            }

            // Color filter
            if (!empty($data['col'])) {
                $q->whereHas('relto_invent', function($query) use ($data) {
                    $query->where('color', $data['col']);
                });
            }

            // Size filter
            if (!empty($data['siz'])) {
                $q->whereHas('relto_invent', function($query) use ($data) {
                    $query->where('size', $data['siz']);
                });
            }
        });

        // Apply sorting
        $sorting = 'created_at';
        $sort_type = 'DESC';

        if (!empty($data['sort'])) {
            switch ($data['sort']) {
                case '1': // Default Sorting (Latest)
                    $sorting = 'created_at';
                    $sort_type = 'DESC';
                    break;
                case '2': // Sort by Name: A-Z
                    $sorting = 'product_name';
                    $sort_type = 'ASC';
                    break;
                case '3': // Sort by Name: Z-A
                    $sorting = 'product_name';
                    $sort_type = 'DESC';
                    break;
                case '4': // Sort by price: Low price
                    $sorting = 'after_disc';
                    $sort_type = 'ASC';
                    break;
                case '5': // Sort by price: High price
                    $sorting = 'after_disc';
                    $sort_type = 'DESC';
                    break;
                default:
                    $sorting = 'created_at';
                    $sort_type = 'DESC';
                    break;
            }
        }

        // Apply showing/pagination
        $showing = 12; // Default
        if (!empty($data['show'])) {
            switch ($data['show']) {
                case '1':
                    $showing = 9;
                    break;
                case '2':
                    $showing = 20;
                    break;
                case '3':
                    $showing = 50;
                    break;
                default:
                    $showing = 12;
                    break;
            }
        }

        $searched_items = $searched_items->orderBy($sorting, $sort_type)->paginate($showing);

        // Check if any actual filter parameters were provided
        $hasFilters = !empty($data['inp']) || !empty($data['cate']) || !empty($data['subcate']) ||
                     !empty($data['brand']) || !empty($data['col']) || !empty($data['siz']) ||
                     !empty($data['min']) || !empty($data['max']);

        if ($hasFilters) {
            $store_items = $searched_items;
        }
        else {
            $store_items = Product_list::orderBy('updated_at', 'DESC')->paginate($showing);
        }

        return view('frontend.shop', [
            'cate_all' => $cate_all,
            'subcate_all' => $subcate_all,
            'color_all' => $color_all,
            'size_all' => $size_all,
            'brand_all' => $brand_all,
            'size_type' => $size_type,
            'store_items' => $store_items,
        ]);
    }





    // === Frontend Contact ===
    function contact_page(){
        return view('frontend.contact');
    }

    // === Handle Contact Form Submission ===
    function handle_contact(Request $request) {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        try {
            // Create email data
            $data = [
                'name' => $request->name,
                'email' => $request->email,
                'subject' => $request->subject,
                'message_content' => $request->message
            ];

            // Send email using Mail facade
            Mail::send('emails.contact', $data, function($message) use ($data) {
                $message->to('<EMAIL>')
                        ->from('<EMAIL>', 'IPAC Telecom Contact Form')
                        ->replyTo($data['email'], $data['name'])
                        ->subject('New Message from ' . $data['name'] . ': ' . $data['subject']);
            });

            return back()->with('success', 'Thank you for your message. We will get back to you soon!');
        } catch (\Exception $e) {
            \Log::error('Contact form error: ' . $e->getMessage());
            return back()->with('error', 'Sorry, there was an error sending your message. Please try again later or contact us <NAME_EMAIL>')
                        ->withInput();
        }
    }

    // === Product Details ===
    function product_details($slug){
        $product_info = Product_list::where('slug', $slug)->get()->first();

        $thumbnail = Thumbnail::where('product_id', $product_info->id)->get();
        $rel_products = Product_list::where('cata_id', $product_info->cata_id)->where('id', '!=', $product_info->id)->get();

        $color_info = Inventory::where('product_id', $product_info->id)
            ->groupBy('color')
            ->selectRaw('sum(color) as sum, color')
            ->get();

        $size_info = Inventory::where('product_id', $product_info->id)
            ->orderBy('size')
            ->groupBy('size')
            ->selectRaw('sum(size) as sum, size')
            ->get();

        $meta = [
            'title' => $product_info->product_name,
            'desc' => $product_info->short_desc,
        ];

        $shareComponent = ShareFacade::currentPage($product_info->product_name)
            ->facebook()
            ->twitter()
            ->linkedin()
            ->telegram()
            ->whatsapp()
            ->reddit();

        return view('frontend.details', [
            'product_info' => $product_info,
            'thumbnail' => $thumbnail,
            'rel_products' => $rel_products,
            'color_info' => $color_info,
            'size_info' => $size_info,
            'shareComponent' => $shareComponent,
            'meta' => $meta,
        ]);
    }

    // === Get Size ===
    function get_size(Request $request){

        $avail_size = Inventory::where('product_id', $request->product_id)->where('color', $request->color_id)->orderBy('size')->get();

        $str = '';
        foreach($avail_size as $size){
            $str .= '<div class="form-check size-option form-option form-check-inline mb-2">
                <input class="form-check-input size_inp" type="radio" name="prod_size" value="'.$size->size.'" id="siz'.$size->relto_size->id.'">
                <label class="form-option-label" for="siz'.$size->relto_size->id.'">'.$size->relto_size->size.'</label>
            </div>';
        }
        echo $str;
    }

    // === Get Quantity Ajax ===
    function get_quantity(Request $request){
        $stock = Inventory::where('product_id', $request->product_id)->where('color', $request->color_id)->where('size', $request->size_id)->get()->first()->quantity;

        $qty = '';
        for ($i=1; $i <= $stock; $i++) {
            $qty .= '<option value="'.$i.'">'.$i.'</option>';
        }
        echo $qty;
    }

    // === Customer Login ===
    function customer_login(){
        return view('frontend.cust_login');
    }

    // === Color_Size for QTY ===
    function get_color_size(Request $request){
        $stock = Inventory::where('product_id', $request->product_id)->where('color', $request->color_id)->where('size', $request->size_id)->get()->first()->quantity;

        echo $stock;
    }

    // === Product Review ===
    function product_review(Request $request, $product_id){
        if(!$request->rating || !$request->review){
            return back()->with([
                'rev_error' => 'rev_error',
            ])->withInput();
        }
        else {
            OrdereditemsTab::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $product_id)->whereNull('review')->first()->update([
                'review' => $request->review,
                'star' => $request->rating,
            ]);

            return back()->with([
                'rev_done' => 'rev_done',
            ]);
        }
    }



    // === Language Select ===
    function lang_eng(){
        session::pull('lang_fra');
        session::pull('lang_ben');
        session::pull('lang_fin');
        return back();
    }

    function lang_fra(){
        session::pull('lang_ben');
        Session([
            'lang_fra' => 'fr',
        ]);
        return back();
    }

    function lang_ben(){
        session::pull('lang_fra');
        Session([
            'lang_ben' => 'bn',
        ]);
        return back();
    }

    function lang_fin(){
        session::pull('lang_fra');
        session::pull('lang_ben');
        Session([
            'lang_fin' => 'fi',
        ]);
        return back();
    }



    function coupon_view(){
        $coupon_all = Coupon::orderBy('id', 'DESC')->get();

        return view('frontend.coupon_view', [
            'coupon_all' => $coupon_all,
        ]);
    }


    function faq_page(){
        $faq_all = FaqTab::orderBy('order')->get();
        return view('frontend.faq', [
            'faq_all' => $faq_all,
        ]);
    }

    /**
     * Show the PWA installation guide page.
     */
    public function pwa_install_guide(){
        return view('frontend.pwa_install_guide');
    }


    function subs_insert(Request $request){
        $request->validate([
            'subs_email' => 'required|email:rfc,dns|unique:subs_tabs,email',
        ], [
            'subs_email.required' => 'Invalid Email!',
            'subs_email.email' => 'Invalid Email!',
            'subs_email.unique' => 'Already Subscribed!',
        ]);
        // return $request->all();

        SubsTab::insert([
            'email' => $request->subs_email,
            'created_at' => Carbon::now(),
        ]);
        return back()->with('subs_done', 'You have been successfully subscribed!');
    }

    function repair_service(){
        return view('frontend.repair_service');
    }

    function submit_repair_request(Request $request){
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'device_type' => 'required|string|max:255',
            'device_model' => 'required|string|max:255',
            'repair_issue' => 'required|string|max:255',
            'message' => 'nullable|string',
        ]);

        try {
            // Create email data
            $data = [
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'device_type' => $request->device_type,
                'device_model' => $request->device_model,
                'repair_issue' => $request->repair_issue,
                'message' => $request->message,
            ];

            // Send email using Mail facade
            Mail::send('emails.repair_request', $data, function($message) use ($data) {
                $message->to('<EMAIL>')
                        ->from('<EMAIL>', 'IPAC Telecom Repair Request')
                        ->replyTo($data['email'], $data['name'])
                        ->subject('New Repair Request from ' . $data['name'] . ': ' . $data['device_type'] . ' - ' . $data['device_model']);
            });

            return back()->with('repair_success', 'Thank you for your repair request. We will contact you shortly with a quote!');
        } catch (\Exception $e) {
            \Log::error('Repair request error: ' . $e->getMessage());
            return back()->with('repair_error', 'Sorry, there was an error sending your request. Please try again later or contact us directly.')
                        ->withInput();
        }
    }

    /**
     * Show the electronics customization page.
     */
    public function customize()
    {
        return view('frontend.customize');
    }

    /**
     * Handle customization request submission.
     */
    public function customize_submit(Request $request)
    {
        try {
            // Validate request
            $validated = $request->validate([
                'devices' => 'required|array|min:1',
                'devices.*.accessory_type_id' => 'required|exists:accessory_types,id',
                'devices.*.device_model_id' => 'required|exists:device_models,id',
                'devices.*.customization_details' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'delivery_location' => 'required|in:finland,international',
                'estimated_cost' => 'required|numeric|min:0',
            ]);

            // Create customization request
            $customizationRequest = DB::transaction(function () use ($validated, $request) {
                $requestModel = CustomizationRequest::create([
                    'user_id' => Auth::guard('cust_login')->check() ? Auth::guard('cust_login')->id() : null,
                    'notes' => $validated['notes'] ?? null,
                    'estimated_cost' => $validated['estimated_cost'],
                    'delivery_location' => $validated['delivery_location'],
                ]);

                foreach ($validated['devices'] as $device) {
                    $requestModel->devices()->create($device);
                }

                // Notify admin users
                User::role('Admin')->get()->each(function ($admin) use ($requestModel) {
                    $admin->notify(new \App\Notifications\CustomizationRequestSubmitted($requestModel));
                });

                return $requestModel;
            });

            return redirect()->back()->with('customize_success', 'Your customization request has been submitted successfully. Our team will contact you soon with final pricing.');
        } catch (\Exception $e) {
            return redirect()->back()->with('customize_error', 'There was an error submitting your request. Please try again.');
        }
    }

    /**
     * Handle customization request submission via API.
     */
    public function customize_submit_api(Request $request)
    {
        try {
            // Log the incoming request for debugging
            \Log::info('Received customization request', [
                'content_type' => $request->header('Content-Type'),
                'request_data' => $request->all()
            ]);

            // For JSON requests, get the data from the JSON payload
            $data = $request->json()->all();
            \Log::info('JSON data', ['data' => $data]);

            // Merge JSON data into request for validation
            $request->merge($data);

            // Validate request
            $validated = $request->validate([
                'devices' => 'required|array|min:1',
                'devices.*.accessory_type_id' => 'required|exists:accessory_types,id',
                'devices.*.accessory_subtype_id' => 'required|exists:accessory_subtypes,id',
                'devices.*.device_model_id' => 'required|exists:device_models,id',
                'devices.*.customization_details' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'delivery_location' => 'required|in:finland,international',
                'estimated_cost' => 'required|numeric|min:0',
                // Allow both nested and direct customer info for flexibility
                'customer_info' => 'sometimes|array',
                'customer_info.name' => 'sometimes|required_with:customer_info|string|max:255',
                'customer_info.email' => 'sometimes|required_with:customer_info|email|max:255',
                'customer_info.phone' => 'sometimes|required_with:customer_info|string|max:20',
                'customer_info.address' => 'sometimes|required_with:customer_info|string|max:255',
                'customer_info.city' => 'sometimes|required_with:customer_info|string|max:255',
                'customer_info.postal_code' => 'sometimes|required_with:customer_info|string|max:20',
                'customer_info.country' => 'sometimes|required_with:customer_info|string|max:100',
                // Also accept direct customer info fields
                'customer_name' => 'sometimes|required_without:customer_info|string|max:255',
                'customer_email' => 'sometimes|required_without:customer_info|email|max:255',
                'customer_phone' => 'sometimes|required_without:customer_info|string|max:20',
                'customer_address' => 'sometimes|required_without:customer_info|string|max:255',
                'customer_city' => 'sometimes|required_without:customer_info|string|max:255',
                'customer_postal_code' => 'sometimes|required_without:customer_info|string|max:20',
                'customer_country' => 'sometimes|required_without:customer_info|string|max:100',
            ]);

            // Create customization request
            $customizationRequest = \DB::transaction(function () use ($validated, $request) {
                // Get customer ID if logged in
                $customerId = Auth::guard('cust_login')->check() ? Auth::guard('cust_login')->id() : null;

                // Extract customer info from the validated data
                $customerInfo = $validated['customer_info'] ?? [];

                // Create the customization request record with default values for required fields
                $requestData = [
                    'user_id' => $customerId, // Use user_id instead of customer_id to match the database schema
                    // Use either nested customer_info or direct fields, with direct fields taking precedence
                    'customer_name' => $validated['customer_name'] ?? ($customerInfo['name'] ?? 'Guest User'),
                    'customer_email' => $validated['customer_email'] ?? ($customerInfo['email'] ?? '<EMAIL>'),
                    'customer_phone' => $validated['customer_phone'] ?? ($customerInfo['phone'] ?? 'Not provided'),
                    'customer_address' => $validated['customer_address'] ?? ($customerInfo['address'] ?? 'Not provided'),
                    'customer_city' => $validated['customer_city'] ?? ($customerInfo['city'] ?? 'Not provided'),
                    'customer_postal' => $validated['customer_postal_code'] ?? ($customerInfo['postal_code'] ?? 'Not provided'),
                    'customer_country' => $validated['customer_country'] ?? ($customerInfo['country'] ?? 'Not provided'),
                    'notes' => $validated['notes'] ?? '',
                    'delivery_location' => $validated['delivery_location'] ?? 'finland',
                    'estimated_cost' => $validated['estimated_cost'] ?? 0,
                    'status' => 'pending',
                ];

                // Log the data being saved for debugging
                \Log::info('Creating customization request with data:', $requestData);

                $requestModel = CustomizationRequest::create($requestData);

                // Create the device customization records
                foreach ($validated['devices'] as $device) {
                    $requestModel->devices()->create([
                        'accessory_type_id' => $device['accessory_type_id'],
                        'accessory_subtype_id' => $device['accessory_subtype_id'],
                        'device_model_id' => $device['device_model_id'],
                        'customization_details' => $device['customization_details'] ?? null,
                    ]);
                }

                // Send email notification to admin
                try {
                    $adminEmail = '<EMAIL>'; // Replace with actual admin email or get from config

                    $data = [
                        'name' => $requestModel->customer_name,
                        'email' => $requestModel->customer_email,
                        'phone' => $requestModel->customer_phone,
                        'address' => $requestModel->customer_address,
                        'city' => $requestModel->customer_city,
                        'postal_code' => $requestModel->customer_postal,
                        'country' => $requestModel->customer_country,
                        'devices' => $validated['devices'],
                        'notes' => $requestModel->notes ?? 'None',
                        'delivery_location' => $requestModel->delivery_location,
                        'estimated_cost' => $requestModel->estimated_cost,
                        'order_id' => $requestModel->id
                    ];

                    \Mail::send('emails.customization_request', $data, function($message) use ($data, $adminEmail) {
                        $message->to($adminEmail)
                                ->from('<EMAIL>', 'IPAC Telecom Customization Order')
                                ->replyTo($data['email'], $data['name'])
                                ->subject('New Customization Order #' . $data['order_id'] . ' from ' . $data['name']);
                    });
                } catch (\Exception $emailError) {
                    // Log the email error but don't fail the transaction
                    \Log::error('Failed to send customization order email: ' . $emailError->getMessage());
                }

                return $requestModel;
            });

            // Clear saved progress after successful submission
            if ($request->cookie('customization_session')) {
                \App\Models\SavedCustomizationProgress::where('session_id', $request->cookie('customization_session'))->delete();
            } elseif (Auth::guard('cust_login')->check()) {
                \App\Models\SavedCustomizationProgress::where('customer_id', Auth::guard('cust_login')->id())->delete();
            }

            return response()->json([
                'success' => true,
                'message' => 'Your customization request has been submitted successfully. Our team will contact you soon with final pricing.',
                'request_id' => $customizationRequest->id
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Customization request validation error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'There was an error with your form submission. Please check the form and try again.',
                'error' => $e->getMessage(),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Customization request error: ' . $e->getMessage());
            \Log::error('Error trace: ' . $e->getTraceAsString());

            // Check for database errors
            if ($e instanceof \PDOException || $e instanceof \Illuminate\Database\QueryException) {
                \Log::error('Database error: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'There was a database error processing your request. Please try again later.',
                    'error' => $e->getMessage(),
                    'error_type' => 'database'
                ], 500);
            }

            return response()->json([
                'success' => false,
                'message' => 'There was an error submitting your request. Please try again.',
                'error' => $e->getMessage(),
                'error_type' => 'general'
            ], 422);
        }
    }
}
