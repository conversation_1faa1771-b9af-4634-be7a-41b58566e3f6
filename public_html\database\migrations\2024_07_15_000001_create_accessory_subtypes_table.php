<?php
declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

final class CreateAccessorySubtypesTable extends Migration
{
    public function up(): void
    {
        Schema::create('accessory_subtypes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('accessory_type_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('image_path')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            // Ensure name is unique per accessory type
            $table->unique(['accessory_type_id', 'name']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('accessory_subtypes');
    }
} 