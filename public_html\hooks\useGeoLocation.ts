import { useState, useEffect } from 'react';

export const useGeoLocation = () => {
  const [countryCode, setCountryCode] = useState<string>('');

  useEffect(() => {
    const getLocation = async () => {
      try {
        const response = await fetch('https://ipapi.co/json/');
        const data = await response.json();
        setCountryCode(data.country_code);
      } catch (error) {
        console.error('Error fetching location:', error);
        setCountryCode('US'); // Default to USD if location detection fails
      }
    };

    getLocation();
  }, []);

  return countryCode;
}; 