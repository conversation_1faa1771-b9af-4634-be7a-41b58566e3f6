<?php

namespace App\Events;

use App\Models\OrderTab;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NewOrderPlaced implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;

    public function __construct(OrderTab $order)
    {
        $this->order = $order;
    }

    public function broadcastOn()
    {
        return new Channel('orders');
    }

    public function broadcastAs()
    {
        return 'new-order';
    }

    public function broadcastWith()
    {
        $customerName = $this->order->relto_custinfo ? $this->order->relto_custinfo->name : ($this->order->guest_name ?? 'Guest');
        return [
            'order_id' => $this->order->order_id,
            'customer_name' => $customerName,
            'total' => $this->order->gtotal,
            'created_at' => $this->order->created_at->diffForHumans()
        ];
    }
} 