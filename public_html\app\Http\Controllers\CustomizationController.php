<?php
declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\AccessoryType;
use App\Models\DeviceModel;
use App\Models\CustomizationRequest;
use App\Models\CustomizationDevice;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Notifications\CustomizationRequestSubmitted;
use App\Models\User;
use App\Models\CustomizationNotification;

final class CustomizationController extends Controller
{
    /**
     * List all accessory types.
     */
    public function accessoryTypes(): JsonResponse
    {
        return response()->json(AccessoryType::all());
    }

    /**
     * List all device models.
     */
    public function deviceModels(): JsonResponse
    {
        return response()->json(DeviceModel::all());
    }

    /**
     * Estimate cost for a customization request.
     */
    public function estimate(Request $request): JsonResponse
    {
        $basePrice = 0.0; //changed base price to 0.0 from 10.0 
        $deliveryFinland = 5.0;
        $deliveryInternational = 15.0;

        $deviceCount = count($request->input('devices', []));
        $delivery = $request->input('delivery_location') === 'finland' ? $deliveryFinland : $deliveryInternational;
        $estimatedCost = $basePrice * $deviceCount + $delivery;

        return response()->json([
            'estimated_cost' => $estimatedCost,
            'note' => '* This is an estimate. Customer service will contact you for final pricing.'
        ]);
    }

    /**
     * Store a new customization request.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'devices' => 'required|array|min:1',
            'devices.*.accessory_type_id' => 'required|exists:accessory_types,id',
            'devices.*.device_model_id' => 'required|exists:device_models,id',
            'devices.*.customization_details' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'delivery_location' => 'required|in:finland,international',
            'estimated_cost' => 'required|numeric|min:0',
        ]);

        return DB::transaction(function () use ($validated) {
            $requestModel = CustomizationRequest::create([
                'user_id' => Auth::id(),
                'notes' => $validated['notes'] ?? null,
                'estimated_cost' => $validated['estimated_cost'],
                'delivery_location' => $validated['delivery_location'],
            ]);

            foreach ($validated['devices'] as $device) {
                $requestModel->devices()->create($device);
            }

            // Notify all admins
            User::role('Admin')->get()->each(function ($admin) use ($requestModel) {
                $admin->notify(new CustomizationRequestSubmitted($requestModel));
            });

            // After successfully creating the customization order
            CustomizationNotification::create([
                'customization_id' => $requestModel->id,
                'type' => 'new_order',
                'message' => "New customization order received from {$requestModel->customer_name}",
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Customization request submitted. * This is an estimate. Customer service will contact you for final pricing.',
                'request_id' => $requestModel->id,
            ]);
        });
    }
} 