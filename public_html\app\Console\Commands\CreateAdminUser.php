<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CreateAdminUser extends Command
{
    protected $signature = 'admin:create';
    protected $description = 'Create a new admin user';

    public function handle()
    {
        $this->info('Create New Admin User');
        $this->info('==================');

        $email = $this->ask('Enter admin email');
        $name = $this->ask('Enter admin name');
        $password = $this->secret('Enter admin password');
        $confirmPassword = $this->secret('Confirm admin password');

        // Validate input
        $validator = Validator::make([
            'email' => $email,
            'name' => $name,
            'password' => $password,
            'password_confirmation' => $confirmPassword,
        ], [
            'email' => ['required', 'email', 'unique:users'],
            'name' => ['required', 'string', 'max:255'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return 1;
        }

        // Create the admin user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
        ]);

        // Assign admin role if using Spatie Permission
        if (method_exists($user, 'assignRole')) {
            $user->assignRole('Admin');
        }

        $this->info('Admin user created successfully!');
        $this->info("Email: {$email}");
        $this->info("Name: {$name}");

        return 0;
    }
} 