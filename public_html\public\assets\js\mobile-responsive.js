/**
 * Mobile Responsive JavaScript for IPAC Telecom
 * Enhances the mobile experience with JavaScript functionality
 */

(function($) {
    "use strict";
    
    // Document ready function
    $(document).ready(function() {
        // Initialize mobile-specific functionality
        initMobileNav();
        adjustCartSidebar();
        fixMobileInputs();
        handleMobileScroll();
        
        // Handle window resize events
        $(window).on('resize', function() {
            adjustCartSidebar();
        });
    });
    
    /**
     * Initialize mobile navigation
     */
    function initMobileNav() {
        // Make sure navigation is scrollable on mobile
        if ($(window).width() <= 768) {
            $('.nav-menus-wrapper').css({
                'overflow-x': 'auto',
                '-webkit-overflow-scrolling': 'touch'
            });
            
            // Prevent navigation links from being too small to tap
            $('.nav-menu > li > a').css({
                'padding': '10px 15px',
                'font-size': '14px'
            });
            
            // Ensure dropdown menus are usable on mobile
            $('.nav-dropdown').on('click', function(e) {
                if ($(window).width() <= 768) {
                    e.preventDefault();
                    $(this).siblings('.nav-dropdown-menu').slideToggle(300);
                }
            });
        }
    }
    
    /**
     * Adjust cart and wishlist sidebars for mobile
     */
    function adjustCartSidebar() {
        if ($(window).width() <= 768) {
            $('.w3-ch-sideBar').css('width', '85%');
        } else {
            $('.w3-ch-sideBar').css('width', '');
        }
    }
    
    /**
     * Fix issues with inputs on mobile devices
     */
    function fixMobileInputs() {
        // Prevent zoom on focus in iOS
        $('input, select, textarea').css('font-size', '16px');
        
        // Improve form usability on mobile
        $('.form-group').css('margin-bottom', '15px');
        
        // Make buttons more tappable
        $('.btn').css({
            'padding': '10px 15px',
            'min-height': '44px'
        });
    }
    
    /**
     * Handle mobile scroll behavior
     */
    function handleMobileScroll() {
        // Smooth scrolling for anchor links
        $('a[href^="#"]:not([href="#"])').on('click', function(e) {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && location.hostname === this.hostname) {
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: target.offset().top - 70
                    }, 800);
                    return false;
                }
            }
        });
        
        // Back to top button visibility
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 300) {
                $('#back2Top').fadeIn();
            } else {
                $('#back2Top').fadeOut();
            }
        });
    }
    
})(jQuery);
