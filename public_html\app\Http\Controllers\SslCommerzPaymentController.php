<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Library\SslCommerz\SslCommerzNotification;

class SslCommerzPaymentController extends Controller
{
    public function index(Request $request)
    {
        $order_info = session('order_info');
        $name = session('name');
        $gtotal = session('gtotal');
        $mobile = session('mobile');
        $order_id = session('order_id');

        $post_data = array();
        $post_data['total_amount'] = $gtotal; # You cant not pay less than 10
        $post_data['currency'] = "EUR"; // Changed to EUR for Euro transactions
        $post_data['tran_id'] = uniqid(); // tran_id must be unique

        # CUSTOMER INFORMATION
        $post_data['cus_name'] = 'Customer Name';
        $post_data['cus_email'] = '<EMAIL>';
        $post_data['cus_add1'] = 'Customer Address';
        $post_data['cus_add2'] = "";
        $post_data['cus_city'] = "";
        $post_data['cus_state'] = "";
        $post_data['cus_postcode'] = "";
        $post_data['cus_country'] = "Bangladesh";
        $post_data['cus_phone'] = '8801XXXXXXXXX';
        $post_data['cus_fax'] = "";

        # SHIPMENT INFORMATION
        $post_data['ship_name'] = "Store Test";
        $post_data['ship_add1'] = "Dhaka";
        $post_data['ship_add2'] = "Dhaka";
        $post_data['ship_city'] = "Dhaka";
        $post_data['ship_state'] = "Dhaka";
        $post_data['ship_postcode'] = "1000";
        $post_data['ship_phone'] = "";
        $post_data['ship_country'] = "Bangladesh";

        $post_data['shipping_method'] = "NO";
        $post_data['product_name'] = "Computer";
        $post_data['product_category'] = "Goods";
        $post_data['product_profile'] = "physical-goods";

        # OPTIONAL PARAMETERS
        $post_data['value_a'] = "ref001";
        $post_data['value_b'] = "ref002";
        $post_data['value_c'] = "ref003";
        $post_data['value_d'] = "ref004";

        $update_product = DB::table('ssl_orders')
            ->where('transaction_id', $post_data['tran_id'])
            ->updateOrInsert([
                'name' => $name,
                'email' => $order_info['email'],
                'phone' => $mobile,
                'order_id' => $order_id,
                'customer_id' => Auth::guard('cust_login')->id(),
                'amount' => $gtotal,
                'status' => 'Pending',
                'address' => $order_info['address'],
                'transaction_id' => $post_data['tran_id'],
                'currency' => $post_data['currency'],
            ]);

        $sslc = new SslCommerzNotification();
        $payment_options = $sslc->makePayment($post_data, 'hosted');

        if (!is_array($payment_options)) {
            print_r($payment_options);
            $payment_options = array();
        }
    }

    public function success(Request $request)
    {
        echo "Transaction is Successful";
        $order_id = session('order_id');

        $tran_id = $request->input('tran_id');
        $amount = $request->input('amount');
        $currency = $request->input('currency');

        $sslc = new SslCommerzNotification();
        $order_details = DB::table('ssl_orders')
            ->where('transaction_id', $tran_id)
            ->select('transaction_id', 'status', 'currency', 'amount')->first();

        if ($order_details->status == 'Pending') {
            $validation = $sslc->orderValidate($request->all(), $tran_id, $amount, $currency);

            if ($validation) {
                $update_product = DB::table('ssl_orders')
                    ->where('transaction_id', $tran_id)
                    ->update(['status' => 'Complete']);

                return redirect()->route('order.complete')->with([
                    'order_conf' => $order_id,
                ]);
            }
        } 
        else {
            return redirect()->route('order.failed');
        }
    }

    public function fail(Request $request)
    {
        $tran_id = $request->input('tran_id');
        $order_id = session('order_id');

        $order_details = DB::table('ssl_orders')
            ->where('transaction_id', $tran_id)
            ->select('transaction_id', 'status', 'currency', 'amount')->first();

        if ($order_details->status == 'Pending') {
            $update_product = DB::table('ssl_orders')
                ->where('transaction_id', $tran_id)
                ->update(['status' => 'Failed']);

            return redirect()->route('order.failed');
        } 
        else if ($order_details->status == 'Processing' || $order_details->status == 'Complete') {
            return redirect()->route('order.complete')->with([
                'order_conf' => $order_id,
            ]);
        }
        else {
            return redirect()->route('order.failed');
        }
    }

    public function cancel(Request $request)
    {
        $tran_id = $request->input('tran_id');
        $order_id = session('order_id');

        $order_details = DB::table('ssl_orders')
            ->where('transaction_id', $tran_id)
            ->select('transaction_id', 'status', 'currency', 'amount')->first();

        if ($order_details->status == 'Pending') {
            $update_product = DB::table('ssl_orders')
                ->where('transaction_id', $tran_id)
                ->update(['status' => 'Canceled']);

            return redirect()->route('order.failed');
        } 
        else if ($order_details->status == 'Processing' || $order_details->status == 'Complete') {
            return redirect()->route('order.complete')->with([
                'order_conf' => $order_id,
            ]);
        } 
        else {
            return redirect()->route('order.failed');
        }


    }

    public function ipn(Request $request)
    {
        #Received all the payement information from the gateway
        if ($request->input('tran_id')) #Check transation id is posted or not.
        {

            $tran_id = $request->input('tran_id');

            #Check order status in order tabel against the transaction id or order id.
            $order_details = DB::table('ssl_orders')
                ->where('transaction_id', $tran_id)
                ->select('transaction_id', 'status', 'currency', 'amount')->first();

            if ($order_details->status == 'Pending') {
                $sslc = new SslCommerzNotification();
                $validation = $sslc->orderValidate($request->all(), $tran_id, $order_details->amount, $order_details->currency);
                if ($validation == TRUE) {
                    /*
                    That means IPN worked. Here you need to update order status
                    in order table as Processing or Complete.
                    Here you can also sent sms or email for successful transaction to customer
                    */
                    $update_product = DB::table('ssl_orders')
                        ->where('transaction_id', $tran_id)
                        ->update(['status' => 'Processing']);

                    echo "Transaction is successfully Completed";
                }
            } else if ($order_details->status == 'Processing' || $order_details->status == 'Complete') {

                #That means Order status already updated. No need to udate database.

                echo "Transaction is already successfully Completed";
            } else {
                #That means something wrong happened. You can redirect customer to your product page.

                echo "Invalid Transaction";
            }
        } else {
            echo "Invalid Data";
        }
    }

}
