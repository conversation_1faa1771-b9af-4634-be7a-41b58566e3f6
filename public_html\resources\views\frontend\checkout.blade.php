@extends('layouts.master')

@section('header_css')
<style>

    /* === Select 2 Custom === */
    .select2-container--default .select2-selection--single{
        height: 52px !important;
        padding: 10px 15px;
        padding-top: 13px;
        border-radius: 1px;
        border-color: #e5e5e5;

        font-size: 1rem;
        line-height: 1.25;
        color: #495057;
        background-color: #fff;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        font-size: 14px;
        position: absolute;
        top: 18px;
        right: 8px;
    }
    #code_view .select2-container--default .select2-selection--single{
        padding: 12px 0 8px 5px !important;
    }

</style>
@endsection

@section('content')
<!-- ======================= Top Breadcrubms ======================== -->
<div class="gray py-3">
    <div class="container">
        <div class="row">
            <div class="colxl-12 col-lg-12 col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{route('home_page')}}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{route('shop_page')}}">Shop</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Checkout</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<!-- ======================= Top Breadcrubms ======================== -->

<!-- ======================= Product Detail ======================== -->


<section class="middle">
    <div class="container">
    
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="sec_title position-relative text-center">
                    <h2 class="off_title">Order Checkout</h2>
                    <h3 class="ft-bold pt-3">Checkout Cart</h3>
                </div>
            </div>
        </div>

        @if(isset($is_guest) && $is_guest)
        <!-- Guest User Banner -->
        <div class="row justify-content-center mb-4">
            <div class="col-xl-10 col-lg-10 col-md-12 col-sm-12">
                <div class="alert alert-info alert-dismissible fade show" role="alert" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 10px; color: white;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user-plus mr-3" style="font-size: 1.5rem;"></i>
                        <div class="flex-grow-1">
                            <h5 class="mb-2" style="color: white;">
                                @if (session('lang_ben'))
                                    দ্রুত চেকআউটের জন্য সাইন আপ করুন!
                                @elseif (session('lang_fin'))
                                    Rekisteröidy nopeampaa kassaa varten!
                                @else
                                    Sign Up for Faster Checkout!
                                @endif
                            </h5>
                            <p class="mb-0" style="color: rgba(255,255,255,0.9);">
                                @if (session('lang_ben'))
                                    একটি অ্যাকাউন্ট তৈরি করুন আপনার ক্রয়ের ইতিহাস সংরক্ষণ করতে, দ্রুত চেকআউট করতে এবং অর্ডার ট্র্যাক করতে।
                                @elseif (session('lang_fin'))
                                    Luo tili tallentaaksesi ostohistoriasi, nopeuttaaksesi kassaa ja seurataksesi tilauksiasi.
                                @else
                                    Create an account to save your purchase history, enable faster checkout, and track your orders.
                                @endif
                            </p>
                        </div>
                        <div class="ml-3">
                            <a href="{{route('customer_login')}}" class="btn btn-light btn-sm mr-2">
                                @if (session('lang_ben'))
                                    সাইন আপ
                                @elseif (session('lang_fin'))
                                    Rekisteröidy
                                @else
                                    Sign Up
                                @endif
                            </a>
                            <a href="{{route('customer_login')}}" class="btn btn-outline-light btn-sm">
                                @if (session('lang_ben'))
                                    সাইন ইন
                                @elseif (session('lang_fin'))
                                    Kirjaudu sisään
                                @else
                                    Sign In
                                @endif
                            </a>
                        </div>
                    </div>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close" style="color: white; opacity: 0.8;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        </div>
        @endif
        
        <form action="{{route('billing.store')}}" method="POST" id="checkout_billing">
            @csrf
            <div class="row justify-content-between">

                {{-- === Billing Form === --}}
                <div class="col-12 col-lg-7 col-md-12">
                    <h5 class="mb-4 ft-medium">Delivery Details</h5>
                    <div class="row mb-2">
                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                            <div class="form-group">
                                <label class="text-dark">Full Name *</label>
                                <input type="text" name="name" class="form-control" placeholder="Receipient Name" value="{{old('name') ? old('name') : (Auth::guard('cust_login')->check() ? Auth::guard('cust_login')->user()->name : '')}}" required maxlength="255">
                                @error('name')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label class="text-dark">Email *</label>
                                <input type="email" name="email" class="form-control" placeholder="Email" value="{{old('email') ? old('email') : (Auth::guard('cust_login')->check() ? Auth::guard('cust_login')->user()->email : '')}}" required maxlength="255">
                                @error('email')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>

                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                            <div class="form-group">
                                <label class="text-dark">Company Name</label>
                                <input type="text" name="company_name" class="form-control" placeholder="Company Name (optional)" value="{{old('company_name')}}" maxlength="255">
                                @error('company_name')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>

                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                            <div class="form-group">
                                <label class="text-dark">Delivery Address *</label>
                                <input type="text" name="delivery_address" class="form-control" placeholder="Address" value="{{old('delivery_address')}}" required maxlength="500">
                                @error('delivery_address')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>

                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label class="text-dark">Country *</label>
                                    <select class="custom-select select2 set_country" name="country" required>
                                    <option value="">-- Select Country --</option>
                                    @foreach ($countries as $country)
                                        <option {{$country->id == old('country') ?'selected' :''}}
                                        value="{{$country->id}}">{{$country->name}}</option>
                                    @endforeach
                                </select>
                                @error('country')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label class="text-dark">City / Town *</label>

                                @if (old('country'))
                                    @php
                                        $sel_city = App\Models\City::where('country_id', old('country'))->get();
                                    @endphp

                                    <select type="text" class="custom-select select2 show_city" name="city" required>
                                        @foreach ($sel_city as $city)
                                            <option {{$city->id == old('city') ?'selected' :''}}
                                            value="{{$city->id}}">{{$city->name}}</option>
                                        @endforeach
                                    </select>
                                @else
                                    <select type="text" class="custom-select select2 show_city" name="city" required>
                                        <option value="">-- Select City --</option>
                                    </select>
                                @endif
                                @error('city')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>

                        <div class="col-xl-2 col-lg-2 col-md-4 col-sm-4 col-4 pr-0">
                            <div class="form-group" id="code_view">
                                <label class="text-dark">Phone Code *</label>
                                <select type="text" class="custom-select select2 show_code" name="code" required>
                                    <option value="">-- --</option>
                                </select>
                                @error('code')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-8 col-sm-8 col-8">
                            <div class="form-group">
                                <label class="text-dark">Mobile Number *</label>
                                <input type="tel" name="mobile" class="form-control" placeholder="Mobile Number" value="{{old('mobile')}}" required minlength="7" maxlength="15" pattern="[0-9]+">
                                @error('mobile')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
                            <div class="form-group">
                                <label class="text-dark">Postal Code *</label>
                                <input type="text" name="postal_code" class="form-control" placeholder="Postal Code" value="{{old('postal_code')}}" required minlength="3" maxlength="10">
                                @error('postal_code')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                            <div class="form-group">
                                <label class="text-dark">Additional Information</label>
                                <textarea name="additional_information" class="form-control ht-50" placeholder="Any special instructions or notes (optional)" maxlength="1000">{{old('additional_information')}}</textarea>
                                @error('additional_information')
                                    <strong class="text-danger err">{{$message}}</strong>
                                @enderror
                            </div>
                        </div>

                        {{-- Guest Registration Option --}}
                        @if(!Auth::guard('cust_login')->check())
                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="create_account" name="create_account" value="1" {{old('create_account') ? 'checked' : ''}}>
                                    <label class="custom-control-label text-dark" for="create_account">
                                        <strong>Create an account for faster checkout next time</strong>
                                    </label>
                                </div>
                            </div>
                        </div>

                        {{-- Password Fields (shown when create account is checked) --}}
                        <div id="password_fields" style="display: none;">
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label class="text-dark">Password *</label>
                                    <input type="password" name="password" class="form-control" placeholder="Enter a strong password" value="{{old('password')}}">
                                    <small class="form-text text-muted">
                                        Password must contain at least 8 characters with uppercase, lowercase, number, and special character.
                                    </small>
                                    @error('password')
                                        <strong class="text-danger err">{{$message}}</strong>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
                                <div class="form-group">
                                    <label class="text-dark">Confirm Password *</label>
                                    <input type="password" name="password_confirmation" class="form-control" placeholder="Confirm your password" value="{{old('password_confirmation')}}">
                                    @error('password_confirmation')
                                        <strong class="text-danger err">{{$message}}</strong>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="col-12 col-lg-4 col-md-12">

                    {{-- === Ordered Items === --}}
                    <div class="d-block mb-3">
                        <h5 class="mb-4">Order Items ({{count($cart_info)}})</h5>
                        <ul class="list-group list-group-sm list-group-flush-y list-group-flush-x mb-4">
                            
                            @foreach ($cart_info as $cart)
                                <li class="list-group-item">
                                    <div class="row align-items-center">
                                        <div class="col-3 p-0" style="border: 1px solid rgba(128, 134, 134, 0.527)">
                                            <!-- Image -->
                                            <a href="{{route('product.details', $cart->relto_product->slug)}}"><img src="{{asset('uploads/product/preview/'.$cart->relto_product->preview)}}" alt="Product Preview" class="img-fluid"></a>
                                        </div>
                                        <div class="col d-flex align-items-center">
                                            <div class="cart_single_caption pl-2">
                                                <h4 class="product_title fs-md ft-medium mb-1 lh-1">{{$cart->relto_product->product_name}}</h4>
                                                <p class="mb-1 lh-1"><span class="text-dark">Size: {{$cart->relto_size->size}}</span></p>
                                                <p class="mb-3 lh-1"><span class="text-dark">Color: {{$cart->relto_color->color_name}}</span></p>
                                                <h4 class="fs-md ft-medium mb-3 lh-1">{{number_format($cart->relto_product->after_disc)}} &#8364; * {{$cart->quantity}} </h4>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                    
                    

                    {{-- === Payment Method === --}}
                    <div class="mb-4">
                        <div class="form-group err_scroll" >
                            <h6>Select Payment Method *</h6>
                            <ul class="no-ul-list">

                                <li>
                                    <input {{old('payment_method') == 1 ?'checked' :''}}
                                    id="c3" class="radio-custom" name="payment_method" value="1" type="radio" required>
                                    <label for="c3" class="radio-custom-label">Cash on Delivery</label>
                                </li>
                                <li>
                                    <input {{old('payment_method') == 2 ?'checked' :''}}
                                    id="c4" class="radio-custom" name="payment_method" value="2" type="radio" required>
                                    <label for="c4" class="radio-custom-label">Pay With SSLCommerz</label>
                                </li>
                                <li>
                                    <input {{old('payment_method') == 3 ?'checked' :''}}
                                     id="c5" class="radio-custom" name="payment_method" value="3" type="radio" required>
                                    <label for="c5" class="radio-custom-label">Pay With Stripe</label>
                                </li>
                            </ul>
                            @error('payment_method')
                                <strong class="text-danger err">{{$message}}</strong>
                            @enderror
                        </div>
                    </div>

                    {{-- === Location Charge === --}}
                    <div class="mb-4">
                        <div class="form-group err_scroll">
                            <h6>Delivery Location *</h6>
                            <ul class="no-ul-list">
                                @foreach($delivery_locations as $location)
                                <li>
                                    <input id="charge_{{ $loop->index }}" class="radio-custom location" name="delivery_charge" type="radio" value="{{ $location->id }}" data-charge="{{ $location->charge }}" data-location="{{ Str::slug($location->location) }}" required {{ old('delivery_charge') == $location->id ? 'checked' : '' }}>
                                    <label for="charge_{{ $loop->index }}" class="radio-custom-label">{{ $location->location }} (€{{ $location->charge }})</label>
                                </li>
                                @endforeach
                            </ul>
                            @error('delivery_charge')
                                <strong class="text-danger err">{{$message}}</strong>
                            @enderror
                        </div>
                    </div>

                    {{-- === Breakdown === --}}
                    {{-- {{session('total').' | '.session('discount').' | '.session('ftotal')}} --}}
                    
                    <div class="card mb-4 gray">
                        <div class="card-body">
                            <ul class="list-group list-group-sm list-group-flush-y list-group-flush-x">
                            <li class="list-group-item d-flex text-dark fs-sm ft-regular">
                                <span>Subtotal</span> <span class="ml-auto text-dark ft-medium">{{number_format(round(session('total') ?? 0), 2)}} &#8364;</span>
                            </li>
                            <li class="list-group-item d-flex text-dark fs-sm ft-regular">
                                <span>Coupon Discount ( - )</span> <span class="ml-auto text-dark ft-medium">{{number_format(round(session('discount') ?? 0), 2)}} &#8364;</span>
                            </li>

                            <li class="list-group-item d-flex text-dark fs-sm ft-regular">
                                <span>Delivery Charge ( + )</span> <span class="ml-auto text-dark ft-medium" id="del_charge">
                                    @if (old('delivery_charge'))
                                        @php
                                            $selected_location = $delivery_locations->firstWhere('id', old('delivery_charge'));
                                            $charge = $selected_location ? $selected_location->charge : 0;
                                        @endphp
                                        {{ number_format(round($charge), 2) }}
                                    @else
                                        {{ number_format(round(0), 2) }}
                                    @endif
                                    &#8364;
                                </span>
                            </li>
                            <li class="list-group-item d-flex text-dark fs-sm ft-regular">
                                <span>Grand Total:</span> <span class="ml-auto text-dark ft-medium number" id="gtotal">
                                    @if (old('delivery_charge'))
                                        @php
                                            $ftotal = session('ftotal') ?? 0;
                                            $selected_location = $delivery_locations->firstWhere('id', old('delivery_charge'));
                                            $charge = $selected_location ? $selected_location->charge : 0;
                                            $old_gtotal = $ftotal + $charge;
                                        @endphp
                                        {{ number_format(round($old_gtotal), 2) }}
                                    @else
                                        {{ number_format(round(session('ftotal') ?? 0), 2) }}
                                    @endif
                                    &#8364;
                                </span>
                            </li>
                            </ul>
                        </div>
                    </div>
                    
                    <input type="hidden" name="subtotal" value="{{session('total') ?? 0}}">
                    <input type="hidden" name="discount" value="{{session('discount') ?? 0}}">
                    <input type="hidden" name="ftotal" value="{{session('ftotal') ?? 0}}">
                    <input type="hidden" name="delivery_charge_amount" value="{{ old('delivery_charge') ? $delivery_locations->firstWhere('id', old('delivery_charge'))->charge : 0 }}">

                    <button class="btn btn-block btn-dark mb-3" id="order_btn">Place Your Order</button>
                </div>
            </div>
        </form>
        
    </div>
</section>
<!-- ======================= Product Detail End ======================== -->
    
@endsection

@section('footer_script')

{{-- === Charge Calculation === --}}
<script>
    $('.location').click(function(){
        var charge = parseFloat($(this).data('charge')) || 0;
        var ftotal = parseFloat({{session('ftotal') ?? 0}}) || 0;
        var gtotal = ftotal + charge;

        $('#del_charge').html(charge.toFixed(2) + ' &#8364;');
        $('#gtotal').html(gtotal.toFixed(2) + ' &#8364;');
        // Update hidden field for backend
        $("input[name='delivery_charge_amount']").val(charge);
    });
</script>

{{-- === Scroll to Error === --}}
<script>
    $('.err').show(function(){
        $(document).ready(function(){
            $("html, body").animate({ 
                scrollTop: $('.err').offset().top -400
            }, 0);
        });
    })
</script>

{{-- === Select2 Search === --}}
<script>
    $(document).ready(function() {
        $('.select2').select2();
        $('b[role="presentation"]').hide();
        $('.select2-selection__arrow').append('<i class="fad fa-sort-up"></i>');
    });
</script>

{{-- === Ajax: Get City/Code === --}}
<script>
    $('.set_country').change(function(){
        var country_id = $(this).val();

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax ({
            url: '/get_city',
            type: 'POST',
            data: {'country_id': country_id},
            
            success: function(data){
                $('.show_city').html(data);
            }
        })

        $.ajax ({
            url: '/get_code',
            type: 'POST',
            data: {'country_id': country_id},
            
            success: function(data){
                $('.show_code').html(data);
            }
        })
    })
</script>

{{-- === Leave Page Alert === --}}
<script>
    $(window).on("beforeunload", function() {
        return "";
    });

    $(document).ready(function() {
        $("#checkout_billing").on("submit", function(e) {
            $(window).off("beforeunload");
            return true;
        });
        $("#del_charge").on("click", function() {
            $(window).off("beforeunload");
            return true;
        });
    });
</script>

{{-- === Order Btn Pre Load === --}}
<script>
    $(document).ready(function () {
        $("#order_btn").click(function () {
            $("#loader").show();
        });
    });
</script>

{{-- === Guest Registration Toggle === --}}
<script>
    $(document).ready(function() {
        // Toggle password fields based on create account checkbox
        $('#create_account').change(function() {
            if ($(this).is(':checked')) {
                $('#password_fields').slideDown();
                $('input[name="password"]').attr('required', true);
                $('input[name="password_confirmation"]').attr('required', true);
            } else {
                $('#password_fields').slideUp();
                $('input[name="password"]').attr('required', false);
                $('input[name="password_confirmation"]').attr('required', false);
                $('input[name="password"]').val('');
                $('input[name="password_confirmation"]').val('');
            }
        });

        // Show password fields if checkbox was checked on page load (after validation error)
        if ($('#create_account').is(':checked')) {
            $('#password_fields').show();
            $('input[name="password"]').attr('required', true);
            $('input[name="password_confirmation"]').attr('required', true);
        }

        // Password strength indicator
        $('input[name="password"]').on('input', function() {
            var password = $(this).val();
            var strength = checkPasswordStrength(password);
            var strengthText = '';
            var strengthClass = '';

            switch(strength) {
                case 0:
                    strengthText = 'Very Weak';
                    strengthClass = 'text-danger';
                    break;
                case 1:
                    strengthText = 'Weak';
                    strengthClass = 'text-warning';
                    break;
                case 2:
                    strengthText = 'Fair';
                    strengthClass = 'text-info';
                    break;
                case 3:
                    strengthText = 'Good';
                    strengthClass = 'text-primary';
                    break;
                case 4:
                    strengthText = 'Strong';
                    strengthClass = 'text-success';
                    break;
            }

            // Remove existing strength indicator
            $(this).siblings('.password-strength').remove();

            if (password.length > 0) {
                $(this).after('<small class="password-strength ' + strengthClass + '">Password Strength: ' + strengthText + '</small>');
            }
        });

        function checkPasswordStrength(password) {
            var strength = 0;

            if (password.length >= 8) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[@$!%*?&]/)) strength++;

            return Math.min(strength, 4);
        }
    });
</script>

{{-- === Comprehensive Form Validation === --}}
<script>
    $(document).ready(function() {
        // Form validation and submit button control
        function validateForm() {
            let isValid = true;
            let firstErrorElement = null;

            // Required text fields
            const requiredFields = [
                { name: 'name', label: 'Full Name' },
                { name: 'email', label: 'Email' },
                { name: 'delivery_address', label: 'Delivery Address' },
                { name: 'mobile', label: 'Mobile Number' },
                { name: 'postal_code', label: 'Postal Code' }
            ];

            // Check required text fields
            requiredFields.forEach(function(field) {
                const input = $(`input[name="${field.name}"]`);
                const value = input.val().trim();

                if (!value) {
                    isValid = false;
                    if (!firstErrorElement) firstErrorElement = input;
                    input.addClass('is-invalid');
                } else {
                    input.removeClass('is-invalid');
                }
            });

            // Check required select fields
            const requiredSelects = [
                { name: 'country', label: 'Country' },
                { name: 'city', label: 'City' },
                { name: 'code', label: 'Phone Code' }
            ];

            requiredSelects.forEach(function(field) {
                const select = $(`select[name="${field.name}"]`);
                const value = select.val();

                if (!value || value === '') {
                    isValid = false;
                    if (!firstErrorElement) firstErrorElement = select;
                    select.addClass('is-invalid');
                } else {
                    select.removeClass('is-invalid');
                }
            });

            // Check payment method
            const paymentMethod = $('input[name="payment_method"]:checked').val();
            if (!paymentMethod) {
                isValid = false;
                const paymentContainer = $('.form-group:has(input[name="payment_method"])');
                if (!firstErrorElement) firstErrorElement = paymentContainer;
                paymentContainer.addClass('payment-error');
            } else {
                $('.form-group:has(input[name="payment_method"])').removeClass('payment-error');
            }

            // Check delivery location
            const deliveryCharge = $('input[name="delivery_charge"]:checked').val();
            if (!deliveryCharge) {
                isValid = false;
                const deliveryContainer = $('.form-group:has(input[name="delivery_charge"])');
                if (!firstErrorElement) firstErrorElement = deliveryContainer;
                deliveryContainer.addClass('delivery-error');
            } else {
                $('.form-group:has(input[name="delivery_charge"])').removeClass('delivery-error');
            }

            // Email validation
            const email = $('input[name="email"]').val();
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (email && !emailRegex.test(email)) {
                isValid = false;
                $('input[name="email"]').addClass('is-invalid');
                if (!firstErrorElement) firstErrorElement = $('input[name="email"]');
            }

            // Mobile number validation (7-15 digits)
            const mobile = $('input[name="mobile"]').val();
            if (mobile && (mobile.length < 7 || mobile.length > 15 || !/^\d+$/.test(mobile))) {
                isValid = false;
                $('input[name="mobile"]').addClass('is-invalid');
                if (!firstErrorElement) firstErrorElement = $('input[name="mobile"]');
            }

            // If guest registration is enabled, validate password fields
            if ($('#create_account').is(':checked')) {
                const password = $('input[name="password"]').val();
                const passwordConfirmation = $('input[name="password_confirmation"]').val();

                if (!password || password.length < 8) {
                    isValid = false;
                    $('input[name="password"]').addClass('is-invalid');
                    if (!firstErrorElement) firstErrorElement = $('input[name="password"]');
                }

                if (password !== passwordConfirmation) {
                    isValid = false;
                    $('input[name="password_confirmation"]').addClass('is-invalid');
                    if (!firstErrorElement) firstErrorElement = $('input[name="password_confirmation"]');
                }
            }

            // Enable/disable submit button
            $('#order_btn').prop('disabled', !isValid);

            // Scroll to first error if validation fails and form was submitted
            if (!isValid && firstErrorElement && window.formSubmitAttempted) {
                $('html, body').animate({
                    scrollTop: firstErrorElement.offset().top - 100
                }, 500);
            }

            return isValid;
        }

        // Real-time validation on input changes
        $('input[required], select[required], input[name="payment_method"], input[name="delivery_charge"]').on('input change', function() {
            validateForm();
        });

        // Initial validation
        validateForm();

        // Form submission handling
        $('#checkout_billing').on('submit', function(e) {
            window.formSubmitAttempted = true;

            if (!validateForm()) {
                e.preventDefault();

                // Show error message
                if (!$('.validation-error-message').length) {
                    $(this).prepend('<div class="alert alert-danger validation-error-message">Please fill in all required fields correctly before placing your order.</div>');
                }

                return false;
            }

            // Remove any existing error messages
            $('.validation-error-message').remove();

            // Disable submit button to prevent double submission
            $('#order_btn').prop('disabled', true).text('Processing...');
        });

        // Remove validation classes on focus
        $('input, select').on('focus', function() {
            $(this).removeClass('is-invalid');
        });
    });
</script>

{{-- === Custom CSS for validation === --}}
<style>
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    .payment-error {
        border: 2px solid #dc3545;
        border-radius: 5px;
        padding: 10px;
        background-color: rgba(220, 53, 69, 0.1);
    }

    .delivery-error {
        border: 2px solid #dc3545;
        border-radius: 5px;
        padding: 10px;
        background-color: rgba(220, 53, 69, 0.1);
    }

    #order_btn:disabled {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
        cursor: not-allowed;
    }

    .validation-error-message {
        margin-bottom: 20px;
    }
</style>
@endsection
