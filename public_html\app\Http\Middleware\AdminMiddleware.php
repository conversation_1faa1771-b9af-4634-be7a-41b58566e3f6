<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Allow access to repair service routes without authentication
        if ($request->is('repair-service') || $request->is('repair-service/*')) {
            return $next($request);
        }
        
        // Check if the user is authenticated
        if (!Auth::check()) {
            return redirect('/amiowner');
        }
        
        // Check if the user is a CustInfo model (customer) instead of a User model (admin)
        if (Auth::user() instanceof \App\Models\CustInfo) {
            Auth::logout();
            return redirect('/amiowner')->with('login_error', 'You do not have permission to access the admin area.');
        }
        
        // Make sure the user has the HasRoles trait
        if (!method_exists(Auth::user(), 'getRoleNames')) {
            Auth::logout();
            return redirect('/amiowner')->with('login_error', 'Your account does not have the required permissions.');
        }
        
        return $next($request);
    }
} 