<?php
declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\AccessoryType;
use App\Models\AccessorySubtype;
use App\Models\DeviceModel;
use App\Models\CustomizationRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

final class CustomizationAdminController extends Controller
{
    // Accessory Types CRUD
    public function accessoryTypesIndex()
    {
        $accessoryTypes = AccessoryType::all();
        return view('admin.customization.accessory_types', compact('accessoryTypes'));
    }

    public function accessoryTypeStore(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|unique:accessory_types,name',
            'name_fi' => 'nullable|string',
            'description' => 'nullable|string',
            'description_fi' => 'nullable|string',
            'image' => 'nullable|image|max:2048', // Allow image upload
            'image_path' => 'nullable|string',
        ]);

        $accessoryType = new AccessoryType();
        $accessoryType->name = $validated['name'];
        $accessoryType->name_fi = $validated['name_fi'] ?? null;
        $accessoryType->description = $validated['description'] ?? null;
        $accessoryType->description_fi = $validated['description_fi'] ?? null;

        // Handle image upload if provided
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            $imagePath = $request->file('image')->store('accessory-types', 'public');
            // Add domain to the path for proper URL formation
            $accessoryType->image_path = asset('storage/' . $imagePath);
        } elseif (isset($validated['image_path']) && !empty($validated['image_path'])) {
            $accessoryType->image_path = $validated['image_path'];
        }

        $accessoryType->save();

        return redirect()->route('admin.customization.accessories')
            ->with('success', 'Accessory type created successfully!');
    }

    public function accessoryTypeUpdate(Request $request, AccessoryType $accessoryType)
    {
        $validated = $request->validate([
            'name' => 'required|string|unique:accessory_types,name,' . $accessoryType->id,
            'name_fi' => 'nullable|string',
            'description' => 'nullable|string',
            'description_fi' => 'nullable|string',
            'image' => 'nullable|image|max:2048', // Allow image upload
            'image_path' => 'nullable|string',
        ]);

        $accessoryType->name = $validated['name'];
        $accessoryType->name_fi = $validated['name_fi'] ?? $accessoryType->name_fi;
        $accessoryType->description = $validated['description'] ?? null;
        $accessoryType->description_fi = $validated['description_fi'] ?? $accessoryType->description_fi;

        // Handle image upload if provided
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            // Delete old image if exists and is a local file
            if ($accessoryType->image_path && str_starts_with($accessoryType->image_path, '/storage/')) {
                $oldPath = str_replace('/storage/', '', $accessoryType->image_path);
                Storage::disk('public')->delete($oldPath);
            }

            $imagePath = $request->file('image')->store('accessory-types', 'public');
            // Add domain to the path for proper URL formation
            $accessoryType->image_path = asset('storage/' . $imagePath);
        } elseif (isset($validated['image_path']) && !empty($validated['image_path'])) {
            $accessoryType->image_path = $validated['image_path'];
        }

        $accessoryType->save();

        return redirect()->route('admin.customization.accessories')
            ->with('success', 'Accessory type updated successfully!');
    }

    public function accessoryTypeDestroy(AccessoryType $accessoryType)
    {
        // Delete associated image if it's a local file
        if ($accessoryType->image_path && str_starts_with($accessoryType->image_path, '/storage/')) {
            $path = str_replace('/storage/', '', $accessoryType->image_path);
            Storage::disk('public')->delete($path);
        }

        $accessoryType->delete();

        return redirect()->route('admin.customization.accessories')
            ->with('success', 'Accessory type deleted successfully!');
    }

    // Accessory Subtypes CRUD
    public function accessorySubtypesIndex(AccessoryType $accessoryType)
    {
        $subtypes = $accessoryType->subtypes;
        return view('admin.customization.accessory_subtypes', compact('accessoryType', 'subtypes'));
    }

    public function accessorySubtypesGetAll(AccessoryType $accessoryType): JsonResponse
    {
        $subtypes = $accessoryType->subtypes()->select(['id', 'name', 'name_fi', 'description', 'description_fi', 'image_path', 'price'])->get();
        return response()->json($subtypes);
    }

    public function accessorySubtypeStore(Request $request, AccessoryType $accessoryType)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'name_fi' => 'nullable|string',
            'description' => 'nullable|string',
            'description_fi' => 'nullable|string',
            'image' => 'nullable|image|max:2048', // Allow image upload
            'image_path' => 'nullable|string',
            'price' => 'nullable|numeric|min:0|max:999999.99',
        ]);

        // Check uniqueness for this accessory type
        $exists = $accessoryType->subtypes()
            ->where('name', $validated['name'])
            ->exists();

        if ($exists) {
            return redirect()->back()
                ->withErrors(['name' => 'A subtype with this name already exists for this accessory type.'])
                ->withInput();
        }

        $subtype = new AccessorySubtype();
        $subtype->accessory_type_id = $accessoryType->id;
        $subtype->name = $validated['name'];
        $subtype->name_fi = $validated['name_fi'] ?? null;
        $subtype->description = $validated['description'] ?? null;
        $subtype->description_fi = $validated['description_fi'] ?? null;
        $subtype->price = $validated['price'] ?? 0;

        // Handle image upload if provided
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            $imagePath = $request->file('image')->store('accessory-subtypes', 'public');
            // Add domain to the path for proper URL formation
            $subtype->image_path = asset('storage/' . $imagePath);
        } elseif (isset($validated['image_path']) && !empty($validated['image_path'])) {
            $subtype->image_path = $validated['image_path'];
        }

        $subtype->save();

        return redirect()->route('admin.customization.accessory-subtypes', $accessoryType->id)
            ->with('success', 'Accessory subtype created successfully!');
    }

    public function accessorySubtypeUpdate(Request $request, AccessoryType $accessoryType, AccessorySubtype $subtype)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'name_fi' => 'nullable|string',
            'description' => 'nullable|string',
            'description_fi' => 'nullable|string',
            'image' => 'nullable|image|max:2048',
            'image_path' => 'nullable|string',
            'price' => 'nullable|numeric|min:0|max:999999.99',
        ]);

        // Check uniqueness for this accessory type
        $exists = $accessoryType->subtypes()
            ->where('name', $validated['name'])
            ->where('id', '!=', $subtype->id)
            ->exists();

        if ($exists) {
            return redirect()->back()
                ->withErrors(['name' => 'A subtype with this name already exists for this accessory type.'])
                ->withInput();
        }

        $subtype->name = $validated['name'];
        $subtype->name_fi = $validated['name_fi'] ?? $subtype->name_fi;
        $subtype->description = $validated['description'] ?? null;
        $subtype->description_fi = $validated['description_fi'] ?? $subtype->description_fi;
        $subtype->price = $validated['price'] ?? $subtype->price;

        // Handle image upload if provided
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            // Delete old image if exists and is a local file
            if ($subtype->image_path && str_starts_with($subtype->image_path, '/storage/')) {
                $oldPath = str_replace('/storage/', '', $subtype->image_path);
                Storage::disk('public')->delete($oldPath);
            }

            $imagePath = $request->file('image')->store('accessory-subtypes', 'public');
            // Add domain to the path for proper URL formation
            $subtype->image_path = asset('storage/' . $imagePath);
        } elseif (isset($validated['image_path']) && !empty($validated['image_path'])) {
            $subtype->image_path = $validated['image_path'];
        }

        $subtype->save();

        return redirect()->route('admin.customization.accessory-subtypes', $accessoryType->id)
            ->with('success', 'Accessory subtype updated successfully!');
    }

    public function accessorySubtypeDestroy(AccessoryType $accessoryType, AccessorySubtype $subtype)
    {
        // Delete associated image if it's a local file
        if ($subtype->image_path && str_starts_with($subtype->image_path, '/storage/')) {
            $path = str_replace('/storage/', '', $subtype->image_path);
            Storage::disk('public')->delete($path);
        }

        $subtype->delete();

        return redirect()->route('admin.customization.accessory-subtypes', $accessoryType->id)
            ->with('success', 'Accessory subtype deleted successfully!');
    }

    // Device Models CRUD
    public function deviceModelsIndex()
    {
        $deviceModels = DeviceModel::all();
        return view('admin.customization.device_models', compact('deviceModels'));
    }

    public function deviceModelStore(Request $request)
    {
        $validated = $request->validate([
            'brand' => 'required|string',
            'model' => 'required|string',
            'device_type' => 'required|string',
            'name' => 'nullable|string',
            'description' => 'nullable|string',
            'image_url' => 'nullable|url',
        ]);

        $deviceModel = new DeviceModel();
        $deviceModel->brand = $validated['brand'];
        $deviceModel->model = $validated['model'];
        $deviceModel->device_type = $validated['device_type'];
        $deviceModel->name = $validated['name'] ?? $validated['brand'] . ' ' . $validated['model'];
        $deviceModel->description = $validated['description'] ?? null;
        $deviceModel->image_url = $validated['image_url'] ?? null;
        $deviceModel->save();

        return redirect()->route('admin.customization.device-models')->with('success', 'Device model added successfully');
    }

    public function deviceModelUpdate(Request $request, DeviceModel $deviceModel)
    {
        $validated = $request->validate([
            'brand' => 'required|string',
            'model' => 'required|string',
            'device_type' => 'required|string',
            'name' => 'nullable|string',
            'description' => 'nullable|string',
            'image_url' => 'nullable|url',
        ]);

        $deviceModel->brand = $validated['brand'];
        $deviceModel->model = $validated['model'];
        $deviceModel->device_type = $validated['device_type'];
        $deviceModel->name = $validated['name'] ?? $validated['brand'] . ' ' . $validated['model'];
        $deviceModel->description = $validated['description'] ?? null;
        $deviceModel->image_url = $validated['image_url'] ?? $deviceModel->image_url;
        $deviceModel->save();

        return redirect()->route('admin.customization.device-models')->with('success', 'Device model updated successfully');
    }

    public function deviceModelDestroy(DeviceModel $deviceModel)
    {
        $deviceModel->delete();

        return redirect()->route('admin.customization.device-models')
            ->with('success', 'Device model deleted successfully!');
    }

    // Customization Requests
    public function requestsIndex()
    {
        $requests = CustomizationRequest::with(['devices'])->latest()->get();
        return view('admin.customization.requests', compact('requests'));
    }

    public function requestShow(CustomizationRequest $customizationRequest)
    {
        $customizationRequest->load(['devices.deviceModel', 'devices.accessoryType', 'devices.accessorySubtype']);
        return response()->json($customizationRequest);
    }

    public function requestUpdateStatus(Request $request, CustomizationRequest $customizationRequest)
    {
        $validated = $request->validate([
            'status' => 'required|string|in:pending,in_progress,ready_for_pickup,completed,cancelled',
            'admin_notes' => 'nullable|string',
            'notify_customer' => 'sometimes|boolean',
        ]);

        $customizationRequest->status = $validated['status'];
        $customizationRequest->admin_notes = $validated['admin_notes'] ?? $customizationRequest->admin_notes;
        $customizationRequest->save();

        // TODO: Handle customer notification if needed
        if ($request->has('notify_customer')) {
            // Send notification logic would go here
        }

        return redirect()->route('admin.customization.requests')
            ->with('success', 'Request status updated successfully!');
    }
}