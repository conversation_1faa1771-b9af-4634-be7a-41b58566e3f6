/**
 * Mobile Menu JavaScript for IPAC Telecom
 * Handles the mobile menu functionality
 */

(function($) {
    "use strict";
    
    // Document ready function
    $(document).ready(function() {
        initMobileMenu();
        initMobileSearch();
    });
    
    /**
     * Initialize mobile menu functionality
     */
    function initMobileMenu() {
        // Toggle mobile menu when hamburger is clicked
        $('.hamburger-menu').on('click', function() {
            $(this).toggleClass('active');
            $('.mobile-nav').toggleClass('active');
            $('.mobile-nav-overlay').toggleClass('active');
            $('body').toggleClass('menu-open');
        });
        
        // Close mobile menu when overlay is clicked
        $('.mobile-nav-overlay').on('click', function() {
            $('.hamburger-menu').removeClass('active');
            $('.mobile-nav').removeClass('active');
            $('.mobile-nav-overlay').removeClass('active');
            $('body').removeClass('menu-open');
        });
        
        // Close mobile menu when close button is clicked
        $('.mobile-nav-close').on('click', function() {
            $('.hamburger-menu').removeClass('active');
            $('.mobile-nav').removeClass('active');
            $('.mobile-nav-overlay').removeClass('active');
            $('body').removeClass('menu-open');
        });
    }
    
    /**
     * Initialize mobile search functionality
     */
    function initMobileSearch() {
        // Toggle mobile search form when search button is clicked
        $('.mobile-search-btn').on('click', function(e) {
            e.preventDefault();
            $('.mobile-search-form').toggleClass('active');
        });
        
        // Close mobile search form when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.mobile-search-btn').length && !$(e.target).closest('.mobile-search-form').length) {
                $('.mobile-search-form').removeClass('active');
            }
        });
    }
    
})(jQuery);
