<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers {
        attemptLogin as protected baseAttemptLogin;
    }

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * Override the attemptLogin method to check if the user is in the right guard
     *
     * @param \Illuminate\Http\Request $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        // Log the attempt
        Log::info('Login attempt details:', [
            'email' => $request->email,
            'guard' => Auth::getDefaultDriver(),
            'exists' => \App\Models\User::where('email', $request->email)->exists()
        ]);

        // Check if using correct guard
        if (Auth::getDefaultDriver() !== 'web') {
            Auth::setDefaultDriver('web');
        }

        // Check if the email exists in the customer table
        $custUser = \App\Models\CustInfo::where('email', $request->email)->first();
        if ($custUser) {
            Log::info('Login blocked - customer account detected');
            $this->incrementLoginAttempts($request);
            $request->session()->flash('login_error', 'This appears to be a customer account. Please use the customer login page.');
            return false;
        }

        // Verify the user exists
        $user = \App\Models\User::where('email', $request->email)->first();
        if (!$user) {
            Log::info('User not found in users table');
            return false;
        }

        // Attempt the login
        $attempt = $this->baseAttemptLogin($request);
        Log::info('Login attempt result:', ['success' => $attempt]);
        
        return $attempt;
    }

    /**
     * Show the application's login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Get the login username to be used by the controller.
     *
     * @return string
     */
    public function username()
    {
        return 'email';
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @return string
     */
    protected function redirectTo()
    {
        return route('home');
    }

    protected function guard()
    {
        return Auth::guard('web');
    }
}
