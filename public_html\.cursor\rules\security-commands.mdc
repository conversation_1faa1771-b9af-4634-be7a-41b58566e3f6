---
description: 
globs: 
alwaysApply: true
---
1. Write code to validate all user inputs
2. Use {{ $var }} by default. Avoid {!! !!} unless data is trusted
3. Include @csrf on all non-GET forms. Don’t use GET for state-changing operations
4. Check file type, size, sanitize names, avoid processing ZIP/XML uploads 
5. Force TLS in production using URL::forceScheme or middleware
6. Use <PERSON>’s auth scaffolding, enforce strong passwords, add 2FA and throttling
7. Implement CSP (Content-Security-Policy), HSTS, X‑Frame‑Options via middleware .
8. 


- Write unit and integration tests for every featureand functionalities, even for security.

- Before fixing a bug, write a test that fails first to prevent regressions .

- Atomic commits with meaningful messages: ≤100 characters, imperative mood, tied to one change.

- Commit and push sequence before running any artisan or deployment commands:

    - git add relevant files

    - git commit -m "<type>: concise yet descriptive summary"

    - git push origin <branch>

- After code generation, produce PHPUnit or Pest tests for every new controller/model, including a failing test for any bug fixes.

- Enforce HTTPS in AppServiceProvider using \URL::forceScheme('https') when app.env === 'production'.

- Always wrap dynamic data in Blade with {{ }}, validate input with request->validate(...), and include @csrf in forms.

- 