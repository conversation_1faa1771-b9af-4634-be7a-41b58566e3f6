<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\category;

class TranslateCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translate:categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Translate category names from English to Bengali and update the database';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        // Define translations for existing English category names.
        // Customize or extend this list as per your categories.
        $translations = [
            'Electronics' => 'ইলেকট্রনিক্স',
            'Books'       => 'বই',
            'Clothing'    => 'পোশাক',
            // Add additional mappings here...
        ];

        // Retrieve all categories.
        $categories = category::all();
        foreach ($categories as $category) {
            if (isset($translations[$category->cata_name])) {
                $translated = $translations[$category->cata_name];
                $category->update(['cata_name_ben' => $translated]);
                $this->info("Updated '{$category->cata_name}' to Bengali as '{$translated}'");
            } else {
                $this->info("No Bengali translation for '{$category->cata_name}'. Skipping...");
            }
        }
        $this->info("All applicable categories have been updated.");
        return 0;
    }
} 