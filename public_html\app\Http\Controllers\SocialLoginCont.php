<?php

namespace App\Http\Controllers;

use App\Models\CustInfo;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Hash;
use Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class SocialLoginCont extends Controller
{
    // === GitHub Login ===
    public function github_redirect(){
        return Socialite::driver('github')
            ->redirectUrl(env('APP_URL') . '/github/callback')
            ->redirect();
    }

    public function github_callback(){
        try {
            $user = Socialite::driver('github')
                ->redirectUrl(env('APP_URL') . '/github/callback')
                ->user();
            
            $finduser = CustInfo::where('github_id', $user->id)->first();
            
            if($finduser){
                Auth::guard('cust_login')->login($finduser);
                return redirect()->route('home_page')->with('user_login', 'user_login');
            }
            else{
                $newUser = CustInfo::create([
                    'name' => $user->name,
                    'email' => $user->email,
                    'github_id'=> $user->id,
                    'password' => Hash::make('123@abc')
                ]);
        
                Auth::guard('cust_login')->login($newUser);
                return redirect()->route('home_page')->with('user_login', 'user_login');
            }
        } catch (Exception $e) {
            return redirect()->route('customer_login')->with('social_error', 'GitHub login failed. Please try again.');
        }
    }

    // === Google Login ===
    public function google_redirect(){
        return Socialite::driver('google')
            ->redirectUrl('https://ipactelecom.xyz/google-callback')
            ->redirect();
    }

    public function google_callback(){
        try {
            Log::info('Google callback initiated with request:', [
                'request' => request()->all(),
                'session' => session()->all()
            ]);

            $user = Socialite::driver('google')
                ->redirectUrl('https://ipactelecom.xyz/google-callback')
                ->user();

            Log::info('Google user data received:', [
                'id' => $user->id,
                'email' => $user->email,
                'name' => $user->name
            ]);

            // Check if user data is valid
            if (!$user || !$user->id || !$user->email) {
                Log::error('Google login failed: Invalid user data received', [
                    'user' => $user ?? null
                ]);
                return redirect()->route('customer_login')->with('social_error', 'Invalid data received from Google. Please try again.');
            }

            // Find or create user logic
            $finduser = CustInfo::where('google_id', $user->id)->orWhere('email', $user->email)->first();

            Log::info('User search result:', [
                'found' => $finduser ? true : false,
                'email' => $user->email,
                'google_id' => $user->id
            ]);

            if ($finduser) {
                Log::info('User exists. Attempting to log in.', ['user_id' => $finduser->id]);
                Auth::guard('cust_login')->login($finduser);
                Log::info('User logged in successfully.');
                return redirect()->route('home_page')->with('user_login', 'user_login');
            } else {
                // Create new user
                Log::info('Creating a new user.');
                $newUser = CustInfo::create([
                    'name' => $user->name,
                    'email' => $user->email,
                    'google_id' => $user->id,
                    'password' => Hash::make(Str::random(16))
                ]);
                Log::info('New user created:', ['user_id' => $newUser->id]);

                Auth::guard('cust_login')->login($newUser);
                Log::info('New user logged in successfully.');
                return redirect()->route('home_page')->with('user_login', 'user_login');
            }
        } catch (Exception $e) {
            Log::error('Google login failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'request' => request()->all(),
                'session' => session()->all()
            ]);
            return redirect()->route('customer_login')->with('social_error', 'Google login failed. Please try again.');
        }
    }

    // === Facebook Login ===
    public function facebook_redirect(){
        return Socialite::driver('facebook')
            ->redirectUrl(env('FACEBOOK_REDIRECT_URI'))
            ->redirect();
    }

    public function facebook_callback(){
        try {
            Log::info('Facebook callback initiated with request:', [
                'request' => request()->all(),
                'session' => session()->all()
            ]);

            $user = Socialite::driver('facebook')
                ->redirectUrl(env('FACEBOOK_REDIRECT_URI'))
                ->user();
            
            Log::info('Facebook user data received:', [
                'id' => $user->id,
                'email' => $user->email,
                'name' => $user->name
            ]);

            // Check if user data is valid
            if (!$user || !$user->id || !$user->email) {
                Log::error('Facebook login failed: Invalid user data received', [
                    'user' => $user ?? null
                ]);
                return redirect()->route('customer_login')->with('social_error', 'Invalid data received from Facebook. Please try again.');
            }

            $finduser = CustInfo::where('facebook_id', $user->id)->first();
            
            Log::info('User search result:', [
                'found' => $finduser ? true : false,
                'email' => $user->email,
                'facebook_id' => $user->id
            ]);
        
            if($finduser){
                Log::info('Existing user found, attempting login', ['user_id' => $finduser->id]);
                Auth::guard('cust_login')->login($finduser);
                return redirect()->route('home_page')->with('user_login', 'user_login');
            }
            else{
                Log::info('Creating new user from Facebook data');
                $newUser = CustInfo::create([
                    'name' => $user->name,
                    'email' => $user->email,
                    'facebook_id'=> $user->id,
                    'password' => Hash::make(Str::random(16))
                ]);
        
                Log::info('New user created', ['user_id' => $newUser->id]);
                Auth::guard('cust_login')->login($newUser);
                return redirect()->route('home_page')->with('user_login', 'user_login');
            }
        } catch (Exception $e) {
            Log::error('Facebook login failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'request' => request()->all(),
                'session' => session()->all()
            ]);
            return redirect()->route('customer_login')->with('social_error', 'Facebook login failed: ' . $e->getMessage());
        }
    }

    // === Twitter Login ===
    public function twitter_redirect(){
        return Socialite::driver('twitter')->redirect();
    }

    public function twitter_callback(){
        try {
            $user = Socialite::driver('twitter')->user();
            
            $finduser = CustInfo::where('twitter_id', $user->id)->first();
        
            if($finduser){
                Auth::guard('cust_login')->login($finduser);
                return redirect()->route('home_page')->with('user_login', 'user_login');
            }
            else{
                $newUser = CustInfo::create([
                    'name' => $user->name,
                    'email' => $user->email,
                    'twitter_id'=> $user->id,
                    'password' => Hash::make('123@abc')
                ]);
        
                Auth::guard('cust_login')->login($newUser);
                return redirect()->route('home_page')->with('user_login', 'user_login');
            }
        } catch (Exception $e) {
            return redirect()->route('customer_login')->with('social_error', 'Twitter login failed. Please try again.');
        }
    }
}
